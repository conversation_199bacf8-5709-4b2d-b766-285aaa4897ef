package com.arapeak.alrbea.Interface;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_ASR_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_DHUHR_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_FAJR_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_ISHA_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_MAGHRIB_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_EVNING_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_EVNING_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_FOR_MORNING_AND_EVENING_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_MORNING_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_MORNING_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.CITATION_FOR_EVENING_TIME_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.CITATION_FOR_MORNING_TIME_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.DEFAULT_FOR_IS_THERE_ATHKER;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.EVENING_TIME_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.HOURS_MILLI_SECOND;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ADD_HALF_HOUR_ISHA_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ADD_HALF_HOUR_ISHA_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_JOMAA_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.MINUTES_MILLI_SECOND;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.MORNING_TIME_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.POST_OR_PRE_DUHA_PRAY_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.POST_OR_PRE_PRAY_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.POST_OR_PRE_TO_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.PRAYER_TIME_ANNOUNCEMENT_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_ASR_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_DHUHR_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_FAJR_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_ISHA_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_MAGHRIB_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_DURATION_OF_TAHAJJUD_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_DURATION_OF_TAHAJJUD_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_DURATION_OF_TAHAJJUD_KEY_H;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_DURATION_OF_TARAWIH_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_DURATION_OF_TARAWIH_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_DURATION_OF_TARAWIH_KEY_H;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_POST_OR_PRE_DUHA_PRAY_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_POST_OR_PRE_PRAY_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TAHAJJUD_PRAYER_TIME_HOUR;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TAHAJJUD_PRAYER_TIME_MINUTE;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ASR_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TIME_OF_ATHKAR_AFTER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TIME_OF_IKAMA_KEY;
import static com.arapeak.alrbea.UI.Activity.MainActivity.LastMaghrib;
import static com.arapeak.alrbea.UI.Activity.MainActivity.timingsAlrabeeaTimes;
import static com.arapeak.alrbea.Utils.getDifferenceTime;
import static com.arapeak.alrbea.Utils.getString;
import static com.arapeak.alrbea.hawk.HawkConstants.AR_LANGUAGE;

import android.util.Log;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.AthkarType;
import com.arapeak.alrbea.Enum.PrayerType;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.TestingFlags;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.orhanobut.hawk.Hawk;


public class PrayerTime {
    public static boolean EnableRamadanForTesting = TestingFlags.IsRamadane;
    public static boolean EnableJomaaForTesting = TestingFlags.IsJomoaa;
    public PrayerType prayerType;

    public PrayerTime(PrayerType type) {
        prayerType = type;
    }

    public static long getAthkarDurationTime(AthkarType type) {
        switch (type) {
            case MorningAthkar:
                return Hawk.get(ATHKARS_MORNING_PRAYER_KEY, ATHKARS_MORNING_PRAYER_DEFAULT) * MINUTES_MILLI_SECOND;
            case EveningAthkar:
                return Hawk.get(ATHKARS_EVNING_PRAYER_KEY, ATHKARS_EVNING_PRAYER_DEFAULT) * MINUTES_MILLI_SECOND;
        }
        return ATHKARS_AFTER_DEFAULT;
    }

    public static long getTimeToAnnounceAthkar(AthkarType type) {
//        boolean is12h = Hawk.get(ConstantsOfApp.IS_CUSTOM_KEY, false);
        int beforeAfter = 0;
        long addTime = 0;
        long athkarTime = 0;

        switch (type) {
            case MorningAthkar:
                beforeAfter = Hawk.get(MORNING_TIME_KEY, 1);// 0 : after Fajr , 1: before Sunrise , 2: after Sunrise
                addTime = Hawk.get(CITATION_FOR_MORNING_TIME_KEY, ATHKARS_FOR_MORNING_AND_EVENING_DEFAULT) * MINUTES_MILLI_SECOND;
                switch (beforeAfter) {
                    case 0:
                        athkarTime = PrayerType.Fajr.prayerTime.getAzanTime() + addTime;
                        break;
                    case 1:
                        athkarTime = PrayerType.Sunrise.prayerTime.getAzanTime() - addTime;
                        break;
                    case 2:
                        athkarTime = PrayerType.Sunrise.prayerTime.getAzanTime() + addTime;
                        break;
                }
                return athkarTime;
            case EveningAthkar:
                beforeAfter = Hawk.get(EVENING_TIME_KEY, 1);// 0 : after Asr , 1: before Maghrib , 2: after Maghrib
                addTime = Hawk.get(CITATION_FOR_EVENING_TIME_KEY, ATHKARS_FOR_MORNING_AND_EVENING_DEFAULT) * MINUTES_MILLI_SECOND;
                switch (beforeAfter) {
                    case 0:
                        athkarTime = PrayerType.Asr.prayerTime.getAzanTime() + addTime;
                        break;
                    case 1:
                        athkarTime = PrayerType.Maghrib.prayerTime.getAzanTime() - addTime;
                        break;
                    case 2:
                        athkarTime = PrayerType.Maghrib.prayerTime.getAzanTime() + addTime;
                        break;
                }
                return athkarTime;
        }
        return 0;
    }

    private static boolean isAllowedToAnnonuceForAthkar(AthkarType type) {
        if (type == AthkarType.MorningAthkar)
            return Hawk.get(ConstantsOfApp.IS_SHOW_CITATION_FOR_MORNING_KEY, DEFAULT_FOR_IS_THERE_ATHKER);
        else if (type == AthkarType.EveningAthkar)
            return Hawk.get(ConstantsOfApp.IS_SHOW_CITATION_FOR_EVENING_KEY, DEFAULT_FOR_IS_THERE_ATHKER);
        return false;
    }

    public static boolean isNowLockingDuringAthkar(AthkarType type) {
        if (!isAllowedToAnnonuceForAthkar(type))
            return false;
        long time = System.currentTimeMillis();
        long athkarTime = getTimeToAnnounceAthkar(type);
        long athkarDuration = getAthkarDurationTime(type);
        return time >= athkarTime && time <= athkarTime + athkarDuration;
    }

    public static long getTimeUntilUnlockAthkar(AthkarType type) {
        return getTimeToAnnounceAthkar(type) + getAthkarDurationTime(type);
    }

    public boolean isRamadan() {
        return EnableRamadanForTesting || Utils.isRamadan();
    }

    public boolean isJomaa() {
        return EnableJomaaForTesting || Utils.isJomaa();
    }

    public String getTime() {
        String time = "00:00";
        switch (prayerType) {
            case Fajr:
                time = timingsAlrabeeaTimes.getFajr();
                break;
            case Sunrise:
            case Duha:
                time = timingsAlrabeeaTimes.getSunrise();
                break;
//            case Jomaa:
            case Dhuhr:
                time = timingsAlrabeeaTimes.getDhuhr();
                break;
            case Asr:
                time = timingsAlrabeeaTimes.getAsr();
                break;
            case Isha:
                time = timingsAlrabeeaTimes.getIsha();
                break;
            case Maghrib:
                time = timingsAlrabeeaTimes.getMaghrib();
                break;
        }
        return time;
    }

    public String getWithAdjustment() {
        String key = ramadanKey() + POST_OR_PRE_TO_KEY + prayerType.getKEY();
        int defaultPost = getDefaultAdjustment();
        //total Adjustment

        long timeMilliseconds = Hawk.get(key, defaultPost) * ConstantsOfApp.MINUTES_MILLI_SECOND;

        //if is isha and ramadan check if enabled "add half hour to isha"
        if (isRamadan() && prayerType == PrayerType.Isha && Hawk.get(IS_ENABLE_ADD_HALF_HOUR_ISHA_KEY, IS_ENABLE_ADD_HALF_HOUR_ISHA_DEFAULT))
            timeMilliseconds += 30 * MINUTES_MILLI_SECOND;

        String time = getTime();
        //tarawih time  = isha time with adjustment
        if (prayerType == PrayerType.Tarawih) {
            long ishaEnd = PrayerType.Isha.prayerTime.getTimeUntilUnlockAthkar();

            //check if tarawih delay enabled
            if (Hawk.get(ConstantsOfApp.TARAWIH_TIME_KEY, 0) == 1) {
                ishaEnd += timeMilliseconds;
            }
            return Utils.getTimeFormatted(ishaEnd, "HH:mm");
        }
        if (prayerType == PrayerType.Tahajjud) {
            int hour = Hawk.get(TAHAJJUD_PRAYER_TIME_HOUR, 0);
            int minute = Hawk.get(TAHAJJUD_PRAYER_TIME_MINUTE, 0);
            return hour + ":" + minute;
        }

        if (Hawk.get(ConstantsOfApp.IS_CUSTOM_KEY, false)) {
            return Utils.convertTimePrayer12(time, timeMilliseconds);
        } else {
            return Utils.convertTimePrayer(time, timeMilliseconds);
        }
    }

    private int getDefaultAdjustment() {
        switch (prayerType) {
            case Duha:
                return isRamadan() ? RAMADAN_POST_OR_PRE_DUHA_PRAY_DEFAULT : POST_OR_PRE_DUHA_PRAY_DEFAULT;
            default:
                return isRamadan() ? RAMADAN_POST_OR_PRE_PRAY_DEFAULT : POST_OR_PRE_PRAY_DEFAULT;
        }
    }

    public boolean isAllowedToAnnounceAzan() {
        if (prayerType == PrayerType.Duha
                || prayerType == PrayerType.Sunrise
                || prayerType == PrayerType.Midnight
                || prayerType == PrayerType.LastThird
                || prayerType == PrayerType.Tarawih
                || prayerType == PrayerType.Tahajjud)
            return false;
        return Hawk.get(ramadanKey()
                        + ConstantsOfApp.IS_ENABLE_KEY
                        + ConstantsOfApp.PRAYER_TIME_ANNOUNCEMENT_KEY
                        + prayerType.getKEY()
                , ConstantsOfApp.IS_ENABLE_PRAYER_TIME_ANNOUNCEMENT_DEFAULT);
    }

    public boolean isNextAzan() {
        if (prayerType == PrayerType.Sunrise
                || prayerType == PrayerType.Tarawih
                || prayerType == PrayerType.Tahajjud)
            return false;

        long time = System.currentTimeMillis();
        long azanTime = getAzanTime();
        return azanTime - time > 0;
    }

    public long getAzanTime() {
        long time = 0;

        switch (prayerType) {
            case Midnight: {
                long prayerTimeLong = 0;
                long fajr = PrayerType.Fajr.prayerTime.getAzanTime();
                if (LastMaghrib > 0 && fajr > LastMaghrib) {
                    prayerTimeLong = LastMaghrib + ((fajr - LastMaghrib) / 2);
                }
            /*
            if (prayerApi.getPrayerList().getPrayerTimesThisMonth(month)
                    .size() == day) {
                prayerTimeLong = Utils.convertDateToLongTimestamp(prayerApi.getPrayerList().getPrayerTimesThisMonth(month + 1)
                                .get(0).getDate().getGregorian().getDate()
                        , "00:00");
            } else {
                prayerTimeLong = Utils.convertDateToLongTimestamp(prayerApi.getPrayerList().getPrayerTimesThisMonth(month)
                                .get(day).getDate().getGregorian().getDate()
                        , "00:00" *//*timingsAlrabeeaTimes.getMidnight()*//*);
            }*/
                time = prayerTimeLong;
                break;
            }
            case LastThird: {
                long prayerTimeLong = 0;
                long fajr = PrayerType.Fajr.prayerTime.getAzanTime();
                if (LastMaghrib > 0 && fajr > LastMaghrib) {
                    prayerTimeLong = LastMaghrib + (((fajr - LastMaghrib) / 3) * 2);
                }
                time = prayerTimeLong;
                break;
            }

//            case Duha:
//            case Sunrise: {
//                Log.e("getAzanTime", "Azan time Duha,Sunrise "+time);
//                break;
//            }
            default: {
                Log.e("getDateError", "null " + (timingsAlrabeeaTimes == null));
                String date = timingsAlrabeeaTimes.getDate();
//            time = Utils.convertDateToLongTimestamp(getWithAdjustment());
                time = Utils.convertDateToLongTimestamp(date, getWithAdjustment());
                break;
            }

        }
        return time;
//            return Utils.convertDateToLongTimestamp(dateAlrabeeaTimes.getGregorian().getDate(), getWithAdjustment());
    }

    public boolean isNowLockingDuringAzan() {
        if (!isAllowedToAnnounceAzan())
            return false;
        long prayerTime = getAzanTime();
        long time = System.currentTimeMillis();
        return time >= prayerTime && time < getTimeUntilUnlockAzan();
    }

    public boolean isBetweenAzanAndIkama() {
        long azanTime = getAzanTime();
        long ikamaTime = getIkamaTime();
        long time = System.currentTimeMillis();
        long prayerEnd = getTimeUntilUnlockPrayer();
        long until = getTimeUntilUnlockAthkar();

//        if (time >= azanTime && time <= ikamaTime)
        Log.e("isBetweenAzanAndIkama", "azan " + azanTime + " ikama " + ikamaTime + "  prayerEnd  " + prayerEnd + "   athkar end  " + until + " -- " + prayerType.getName());


        return time >= azanTime && time <= ikamaTime;
    }

    public long getTimeUntilUnlockAzan() {
        return getAzanTime() + (PRAYER_TIME_ANNOUNCEMENT_DEFAULT * MINUTES_MILLI_SECOND);
    }

    public int getDefaultTimeBetweenAdanAndIkama() {
        int time = TIME_BETWEEN_ADAN_AND_IKAMA_DEFAULT;
        switch (prayerType) {
            case Fajr:
                time = TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_DEFAULT;
                break;
            case Tarawih:
            case Tahajjud:
            case Duha:
                time = 0;
                break;
            case Dhuhr:
                if (isJomaa())
                    time = TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_DEFAULT;
                else
                    time = TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_DEFAULT;
                break;
            case Asr:
                time = TIME_BETWEEN_ADAN_AND_IKAMA_ASR_DEFAULT;
                break;
            case Maghrib:
                time = TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_DEFAULT;
                break;
            case Isha:
                time = TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_DEFAULT;
                break;
//            case Jomaa:
//                time = TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_DEFAULT;
//                break;
        }
        return time;
    }

    public long getTimeBetweenAdanAndIkama() {
        String key = ramadanKey();
        key += TIME_BETWEEN_ADAN_AND_IKAMA_KEY + prayerType.getKEY();
        return Hawk.get(key, getDefaultTimeBetweenAdanAndIkama()) * ConstantsOfApp.MINUTES_MILLI_SECOND;
    }

    public String getJomaaIkama() {

        long timeBetweenAdanAndIkama = 0;
        if (isAllowedToAnnounceIkama())
            timeBetweenAdanAndIkama = getIkamaAnnouncementDuration();

        if (isEnableFixedTimeOfIkama())
            return Utils.convertTimePrayer(getFixedTimeOfIkama(), timeBetweenAdanAndIkama);

        if (Hawk.get(ConstantsOfApp.IS_CUSTOM_KEY, false))
            return Utils.convertTimePrayer12(getWithAdjustment(), timeBetweenAdanAndIkama);
        return Utils.convertTimePrayer(getWithAdjustment(), timeBetweenAdanAndIkama);
    }

    public String getIkama() {
        if (isJomaa() && prayerType == PrayerType.Dhuhr)
            return getJomaaIkama();

        if (isEnableFixedTimeOfIkama())
            return getFixedTimeOfIkama();

        long timeBetweenAdanAndIkama = 0;
        if (isEnableTimeBetweenAdanAndIkama())
            timeBetweenAdanAndIkama = getTimeBetweenAdanAndIkama();

        if (Hawk.get(ConstantsOfApp.IS_CUSTOM_KEY, false))
            return Utils.convertTimePrayer12(getWithAdjustment(), timeBetweenAdanAndIkama);
        else
            return Utils.convertTimePrayer(getWithAdjustment(), timeBetweenAdanAndIkama);
    }

    public String getFixedTimeOfIkama() {
        String key = ramadanKey();
        key += TIME_OF_IKAMA_KEY + prayerType.getKEY();
        return Hawk.get(key, ConstantsOfApp.RAMADAN_TIME_OF_IKAMA_DEFAULT);
    }

    public int getDefaultTimeToIkamaAnnouncement() {
        switch (prayerType) {
            case Fajr:
                return isRamadan()
                        ? RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_FAJR_DEFAULT
                        : ANNOUNCEMENT_SHOW_TIME_IKAMA_FAJR_DEFAULT;
            case Dhuhr:
                return isRamadan()
                        ? RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_DHUHR_DEFAULT
                        : ANNOUNCEMENT_SHOW_TIME_IKAMA_DHUHR_DEFAULT;
            case Asr:
                return isRamadan()
                        ? RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_ASR_DEFAULT
                        : ANNOUNCEMENT_SHOW_TIME_IKAMA_ASR_DEFAULT;
            case Maghrib:
                return isRamadan()
                        ? RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_MAGHRIB_DEFAULT
                        : ANNOUNCEMENT_SHOW_TIME_IKAMA_MAGHRIB_DEFAULT;
            case Isha:
                return isRamadan()
                        ? RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_ISHA_DEFAULT
                        : ANNOUNCEMENT_SHOW_TIME_IKAMA_ISHA_DEFAULT;
        }
        return 0;
    }

    public boolean isEnableTimeBetweenAdanAndIkama() {
        String key = ramadanKey();
        key += IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + prayerType.getKEY();
        return Hawk.get(key, ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DEFAULT);
    }

    public boolean isEnableFixedTimeOfIkama() {
        String prayerEnableKey = ramadanKey();
        prayerEnableKey += IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + prayerType.getKEY();
        return Hawk.get(prayerEnableKey, ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_DEFAULT);
    }

    public boolean isNextIkama() {
        if (prayerType == PrayerType.Sunrise
                || prayerType == PrayerType.LastThird
                || prayerType == PrayerType.Midnight)
            return false;

        long time = System.currentTimeMillis();
        long ikamaTime = getIkamaTime();
        long azanTime = getAzanTime();
        if (isRamadan() && (prayerType == PrayerType.Tarawih || prayerType == PrayerType.Tahajjud)) {
            boolean isEnabled = Hawk.get(ramadanKey() + IS_ENABLE_KEY + prayerType.getKEY(), false);
            if (isEnabled)
                return time <= ikamaTime;
            else return false;
        }
        if (prayerType == PrayerType.Duha)
            return time < ikamaTime;
        return time >= azanTime && time < ikamaTime;
//        return getIkamaTime() - time > 0;
    }

    public long getIkamaTime() {
        return Utils.convertDateToLongTimestamp(Utils.getCurrentDate("dd-MM-yyyy"), getIkama());
//        return Utils.convertDateToLongTimestamp(dateAlrabeeaTimes.getGregorian().getDate(), getIkama());
    }

    public String getTimeRemainForAzan(boolean longString) {
        boolean isSunrise = prayerType == PrayerType.Sunrise;
        boolean isMidnight = prayerType == PrayerType.Midnight;
        boolean isLastThird = prayerType == PrayerType.LastThird;
        boolean isTimePrayerApproaching = false;
        long azanTime = getAzanTime();
        String nameOfPrayer = prayerType.getName();
        String timeRemain = "";
        String differenceTime = Utils.getDifferenceTime(azanTime);

        if (getAzanTime() - System.currentTimeMillis() <= MINUTES_MILLI_SECOND)
            isTimePrayerApproaching = true;

        if (differenceTime.isEmpty()) {
            switch (prayerType) {
                case Sunrise:
                    return getString(R.string.now_it_is_time_for_sunrise);
                case Midnight:
                    return getString(R.string.now_it_is_time_for_midnight);
                case LastThird:
                    return getString(R.string.now_it_is_time_for_last_third);
                default:
                    return getString(R.string.now_it_is_time_for_prayer) + " " + nameOfPrayer;
            }
            /*if (isSunrise) {
                return getString(R.string.now_it_is_time_for_sunrise);
            } else if (isMidnight) {
                return getString(R.string.now_it_is_time_for_midnight);
            } else if(isLastThird){}else {
                Hawk.put(TIME_OF_ATHKAR_AFTER_KEY, System.currentTimeMillis());
                return getString(R.string.now_it_is_time_for_prayer) + " " + nameOfPrayer;
            }*/
        } else {
            if (HawkSettings.getTypeNumber().equals(AR_LANGUAGE)) {
                differenceTime = Utils.replaceEnglishNumberToArabicNumber(differenceTime);
            }

            if (isTimePrayerApproaching) {
                if (!isMidnight && !isSunrise) {
                    Hawk.put(TIME_OF_ATHKAR_AFTER_KEY, System.currentTimeMillis());
                }

                timeRemain = getString(R.string.less_than_a_minute_remaining) + " ";
                if (isMidnight)
                    timeRemain += getString(R.string.for_midnight);
                else if (isLastThird)
                    timeRemain += getString(R.string.for_last_third);
                else if (isSunrise)
                    timeRemain += getString(R.string.for_sunrise);
                else if (longString)
                    timeRemain += getString(R.string.for_prayer) + " " + nameOfPrayer;
            } else if (isMidnight) {
                timeRemain = getString(R.string.remaining) + " " + getString(R.string.for_midnight) + " " + differenceTime;
            } else if (isLastThird) {
                timeRemain = getString(R.string.remaining) + " " + getString(R.string.for_last_third) + " " + differenceTime;
            } else if (isSunrise) {
                timeRemain = getString(R.string.remaining) + " " + getString(R.string.for_sunrise) + " " + differenceTime;
            } else if (longString) {
                timeRemain = getString(R.string.remaining_for_prayer) + " " + nameOfPrayer + " " + differenceTime;
            } else {
                timeRemain = getString(R.string.remaining) + " " + differenceTime;
            }
        }
        return timeRemain;
    }

    public String getTimeRemainForIkama() {
        if (prayerType == PrayerType.Duha)
            return "متبقي للضحى" + " " + getDifferenceTime(getIkamaTime());
        return getString(R.string.remaining_for_prayer_ikama) + " "
                + prayerType.getName() + " " + getDifferenceTime(getIkamaTime());
    }

    public boolean isAthkarEnabled() {
        if (prayerType == PrayerType.Sunrise
                || prayerType == PrayerType.Duha
                || prayerType == PrayerType.Midnight
                || prayerType == PrayerType.LastThird
                || prayerType == PrayerType.Tarawih
                || prayerType == PrayerType.Tahajjud)
            return false;
        return Hawk.get(IS_ENABLE_KEY + ATHKARS_AFTER_KEY + prayerType.getKEY(), IS_ENABLE_ATHKARS_AFTER_PRAYER_DEFAULT);
    }

    public long getAthkarTime() {
        return Hawk.get(ATHKARS_AFTER_KEY + prayerType.getKEY(), ATHKARS_AFTER_DEFAULT) * MINUTES_MILLI_SECOND;
    }

    public boolean isNowLockingDuringAthkar() {
        if (!isAthkarEnabled())
            return false;
        long prayerEnd = getTimeUntilUnlockPrayer();
        long until = getTimeUntilUnlockAthkar();
        long time = System.currentTimeMillis();
        Log.e("isNowLockingDuringAthkar", "prayerEnd " + prayerEnd + " " + "until " + until + " time " + time);
        return time >= prayerEnd && time <= until;
    }

    public boolean isNowAnnouncingForIkama() {
        if (!isAllowedToAnnounceIkama())
            return false;
        long ikama = getIkamaTime();
        long timeToStartAnnouncing = getTimeToAnnounceIkama();//ikama annunciation delay
//        long timeToStartAnnouncing = getTimeToAnnounceIkama() - 5000;//ikama annunciation delay
        long time = System.currentTimeMillis();
        return time >= timeToStartAnnouncing && time < ikama;
    }

    public boolean isNowLockDuringPrayer() {
        if (!isEnableLockingDuringPrayer())
            return false;

        long ikama = getIkamaTime();
        long until = getTimeUntilUnlockPrayer();
        long time = System.currentTimeMillis();

        return time >= ikama && time < until;
//        boolean isNowLockDuringPrayer = (getIkamaTime() + getLockTimeDuringPrayer()) - System.currentTimeMillis() > 0;
//        return isNowLockDuringPrayer;
    }

    public boolean isDuringPrayer() {
        long ikama = getIkamaTime();
        long until = getTimeUntilUnlockPrayer();
        long time = System.currentTimeMillis();

        if (time >= ikama && time < until)
            android.util.Log.e("isDuringPrayer", "ikama " + ikama + "  getTimeUntilUnlockPrayer " + until + "  current " + time + "  " + prayerType.getName());

        return time >= ikama && time < until;
//        boolean isNowLockDuringPrayer = (getIkamaTime() + getLockTimeDuringPrayer()) - System.currentTimeMillis() > 0;
//        return isNowLockDuringPrayer;
    }

    public long getTimeUntilUnlockAthkar() {
        return getTimeUntilUnlockPrayer() + getAthkarTime();
    }

    public long getTimeUntilUnlockPrayer() {
        long ikama = getIkamaTime();
        long lock = getLockTimeDuringPrayer();
        return ikama + lock;
    }

    public long getLockTimeDuringPrayer() {
        if (prayerType == PrayerType.Duha) {
            return 5 * MINUTES_MILLI_SECOND;
        }
        if (prayerType == PrayerType.Tarawih) {
            return Hawk.get(RAMADAN_DURATION_OF_TARAWIH_KEY_H, 0) * HOURS_MILLI_SECOND
                    + Hawk.get(RAMADAN_DURATION_OF_TARAWIH_KEY, RAMADAN_DURATION_OF_TARAWIH_DEFAULT) * MINUTES_MILLI_SECOND;
        } else if (prayerType == PrayerType.Tahajjud) {
            return Hawk.get(RAMADAN_DURATION_OF_TAHAJJUD_KEY_H, 0) * HOURS_MILLI_SECOND
                    + Hawk.get(RAMADAN_DURATION_OF_TAHAJJUD_KEY, RAMADAN_DURATION_OF_TAHAJJUD_DEFAULT) * MINUTES_MILLI_SECOND;
        }
        return Hawk.get(ramadanKey()
                        + ConstantsOfApp.LOCK_DURING_PRAYER_KEY
                        + prayerType.getKEY()//(prayerType == PrayerType.Fajr?"":prayerType.KEY))
                , Utils.getTimeToFinishThePrayer(prayerType.getKEY(), isRamadan())) * MINUTES_MILLI_SECOND;
    }

    public boolean isEnableLockingDuringPrayer() {
        if (prayerType == PrayerType.Sunrise
                || prayerType == PrayerType.LastThird
                || prayerType == PrayerType.Midnight)
            return false;
        if (prayerType == PrayerType.Tarawih || prayerType == PrayerType.Tahajjud) {
            return (isRamadan() && Hawk.get(ramadanKey() + IS_ENABLE_KEY + prayerType.getKEY(), false));
        }
        return Hawk.get(ramadanKey() + ConstantsOfApp.IS_ENABLE_KEY
                        + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + prayerType.getKEY()
                , ConstantsOfApp.IS_ENABLE_LOCK_DURING_PRAYER_DEFAULT);
    }

    public boolean isAllowedToAnnounceIkama() {
//        if(!isEnableTimeBetweenAdanAndIkama()
        if (prayerType == PrayerType.Duha
                || prayerType == PrayerType.Sunrise
                || prayerType == PrayerType.Midnight
                || prayerType == PrayerType.LastThird
                || prayerType == PrayerType.Tarawih
                || prayerType == PrayerType.Tahajjud)
            return false;
        return Hawk.get(ramadanKey()
                        + ConstantsOfApp.IS_ENABLE_KEY
                        + ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_KEY
                        + prayerType.getKEY()
                , isJomaa() && prayerType == PrayerType.Dhuhr ? IS_ENABLE_JOMAA_DEFAULT
                        : ConstantsOfApp.IS_ENABLE_ANNOUNCEMENT_SHOW_TIME_IKAMA_DEFAULT);
    }

    public long getIkamaAnnouncementDuration() {
        return Hawk.get(ramadanKey() + ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_KEY + prayerType.getKEY()
                , getDefaultTimeToIkamaAnnouncement()) * MINUTES_MILLI_SECOND;
    }

    public long getTimeToAnnounceIkama() {
        //the announcement is before ikama time by announce duration
        long ikamaTime = getIkamaTime();
        long duration = getIkamaAnnouncementDuration();
        return ikamaTime - duration;
    }

    private String ramadanKey() {
        if (isRamadan())
            return RAMADAN_KEY;
        return "";
    }

    public long getTimeToAnnouncement() {
        double prayerTimeToAnnouncement =
                (Math.abs((getAzanTime() + (MINUTES_MILLI_SECOND * PRAYER_TIME_ANNOUNCEMENT_DEFAULT)) - System.currentTimeMillis()))
                        / (MINUTES_MILLI_SECOND * 1.0);
        int prayerTimeToAnnouncementInt = (int) prayerTimeToAnnouncement;
        if (prayerTimeToAnnouncement > prayerTimeToAnnouncementInt)
            ++prayerTimeToAnnouncementInt;
        return prayerTimeToAnnouncementInt;
    }
}
